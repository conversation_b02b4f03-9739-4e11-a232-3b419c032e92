Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to crc.o(i.MX_CRC_Init) for MX_CRC_Init
    main.o(i.main) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    crc.o(i.MX_CRC_Init) refers to stm32f4xx_hal_crc.o(i.HAL_CRC_Init) for HAL_CRC_Init
    crc.o(i.MX_CRC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    crc.o(i.MX_CRC_Init) refers to crc.o(.data) for .data
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_hal_crc.o(i.HAL_CRC_DeInit) refers to crc.o(i.HAL_CRC_MspDeInit) for HAL_CRC_MspDeInit
    stm32f4xx_hal_crc.o(i.HAL_CRC_Init) refers to crc.o(i.HAL_CRC_MspInit) for HAL_CRC_MspInit
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing crc.o(.rev16_text), (4 bytes).
    Removing crc.o(.revsh_text), (4 bytes).
    Removing crc.o(.rrx_text), (6 bytes).
    Removing crc.o(i.HAL_CRC_MspDeInit), (28 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (44 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_crc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_crc.o(i.HAL_CRC_Accumulate), (38 bytes).
    Removing stm32f4xx_hal_crc.o(i.HAL_CRC_Calculate), (46 bytes).
    Removing stm32f4xx_hal_crc.o(i.HAL_CRC_DeInit), (50 bytes).
    Removing stm32f4xx_hal_crc.o(i.HAL_CRC_GetState), (4 bytes).
    Removing stm32f4xx_hal_crc.o(i.HAL_CRC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_crc.o(i.HAL_CRC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam), (84 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort), (146 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT), (36 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (416 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Init), (212 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (110 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler), (640 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError), (14 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndRxTransfer), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Receive_IT), (194 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (114 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).

270 unused section(s) (total 15360 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/crc.c                        0x00000000   Number         0  crc.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_crc.c 0x00000000   Number         0  stm32f4xx_hal_crc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\Core\Src\crc.c                        0x00000000   Number         0  crc.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_crc.c 0x00000000   Number         0  stm32f4xx_hal_crc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08020000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08020188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08020190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080201c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080201e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080201fc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080201fe   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08020202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08020202   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08020204   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08020206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08020206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08020206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08020206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08020206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08020206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08020206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08020206   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08020208   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08020208   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08020208   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0802020e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0802020e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08020212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08020212   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0802021a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0802021c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0802021c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08020220   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08020228   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x08020228   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08020268   Section      238  lludivv7m.o(.text)
    .text                                    0x08020356   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080203a4   Section        0  heapauxi.o(.text)
    .text                                    0x080203aa   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080203f4   Section        0  exit.o(.text)
    .text                                    0x08020408   Section        8  libspace.o(.text)
    .text                                    0x08020410   Section        0  sys_exit.o(.text)
    .text                                    0x0802041c   Section        2  use_no_semi.o(.text)
    .text                                    0x0802041e   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x0802041e   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08020420   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x08020422   Section        0  main.o(i.Error_Handler)
    i.HAL_CRC_Init                           0x08020426   Section        0  stm32f4xx_hal_crc.o(i.HAL_CRC_Init)
    i.HAL_CRC_MspInit                        0x08020448   Section        0  crc.o(i.HAL_CRC_MspInit)
    i.HAL_Delay                              0x08020470   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08020494   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_TogglePin                     0x08020684   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08020694   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080206a0   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x080206ac   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080206bc   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080206f0   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08020730   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_SetPriority                   0x08020760   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080207a0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x080207c4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080208f8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08020918   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08020938   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08020998   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08020d04   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UART_Init                          0x08020d2c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08020d90   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HardFault_Handler                      0x08020dfc   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_CRC_Init                            0x08020e00   Section        0  crc.o(i.MX_CRC_Init)
    i.MX_GPIO_Init                           0x08020e24   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USART1_UART_Init                    0x08020e90   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08020ec8   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08020eca   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08020ecc   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08020ece   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08020ed0   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08020ed4   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08020f68   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.UART_SetConfig                         0x08020f78   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08020f79   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UsageFault_Handler                     0x08021084   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x08021086   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08021087   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.main                                   0x080210a8   Section        0  main.o(i.main)
    x$fpl$fpinit                             0x080210d8   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080210d8   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x080210e2   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x080210f2   Section        8  system_stm32f4xx.o(.constdata)
    .data                                    0x20000000   Section        8  crc.o(.data)
    .data                                    0x20000008   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000014   Section        4  system_stm32f4xx.o(.data)
    .bss                                     0x20000018   Section       72  usart.o(.bss)
    .bss                                     0x20000060   Section       96  libspace.o(.bss)
    HEAP                                     0x200000c0   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x200000c0   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x200002c0   Section     4096  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x200002c0   Data        4096  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x200012c0   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08020000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08020188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08020189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08020191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08020191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08020191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0802019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080201c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080201e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080201fd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080201ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08020203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08020205   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08020207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08020207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08020207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08020207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08020207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08020207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08020207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08020207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08020209   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08020209   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08020209   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0802020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0802020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08020213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08020213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0802021b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0802021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0802021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08020221   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08020229   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART1_IRQHandler                        0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x08020243   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x08020245   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08020269   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08020269   Thumb Code   238  lludivv7m.o(.text)
    __aeabi_memclr4                          0x08020357   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08020357   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08020357   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0802035b   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080203a5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080203a7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080203a9   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x080203ab   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080203f5   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08020409   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08020409   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08020409   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08020411   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0802041d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0802041d   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x0802041f   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x0802041f   Thumb Code     0  indicate_semi.o(.text)
    DebugMon_Handler                         0x08020421   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x08020423   Thumb Code     4  main.o(i.Error_Handler)
    HAL_CRC_Init                             0x08020427   Thumb Code    34  stm32f4xx_hal_crc.o(i.HAL_CRC_Init)
    HAL_CRC_MspInit                          0x08020449   Thumb Code    30  crc.o(i.HAL_CRC_MspInit)
    HAL_Delay                                0x08020471   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08020495   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x08020685   Thumb Code    16  stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08020695   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080206a1   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x080206ad   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080206bd   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080206f1   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08020731   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_SetPriority                     0x08020761   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080207a1   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x080207c5   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080208f9   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08020919   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08020939   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08020999   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08020d05   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UART_Init                            0x08020d2d   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08020d91   Thumb Code    94  usart.o(i.HAL_UART_MspInit)
    HardFault_Handler                        0x08020dfd   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_CRC_Init                              0x08020e01   Thumb Code    28  crc.o(i.MX_CRC_Init)
    MX_GPIO_Init                             0x08020e25   Thumb Code    98  gpio.o(i.MX_GPIO_Init)
    MX_USART1_UART_Init                      0x08020e91   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08020ec9   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08020ecb   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08020ecd   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08020ecf   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08020ed1   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08020ed5   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x08020f69   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    UsageFault_Handler                       0x08021085   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    main                                     0x080210a9   Thumb Code    44  main.o(i.main)
    _fp_init                                 0x080210d9   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080210e1   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080210e1   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    AHBPrescTable                            0x080210e2   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x080210f2   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x080210fc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0802111c   Number         0  anon$$obj.o(Region$$Table)
    hcrc                                     0x20000000   Data           8  crc.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x2000000c   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000010   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000014   Data           4  system_stm32f4xx.o(.data)
    huart1                                   0x20000018   Data          72  usart.o(.bss)
    __libspace_start                         0x20000060   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200000c0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08020189

  Load Region LR_IROM1 (Base: 0x08020000, Size: 0x00001134, Max: 0x000e0000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08020000, Load base: 0x08020000, Size: 0x0000111c, Max: 0x000e0000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08020000   0x08020000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08020188   0x08020188   0x00000008   Code   RO         1977  * !!!main             c_w.l(__main.o)
    0x08020190   0x08020190   0x00000034   Code   RO         2146    !!!scatter          c_w.l(__scatter.o)
    0x080201c4   0x080201c4   0x0000001a   Code   RO         2148    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080201de   0x080201de   0x00000002   PAD
    0x080201e0   0x080201e0   0x0000001c   Code   RO         2150    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080201fc   0x080201fc   0x00000002   Code   RO         2014    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080201fe   0x080201fe   0x00000004   Code   RO         2026    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2029    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2032    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2034    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2036    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2039    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2041    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2043    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2045    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2047    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2049    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2051    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2053    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2055    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2057    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2059    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2063    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2065    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2067    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000000   Code   RO         2069    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08020202   0x08020202   0x00000002   Code   RO         2070    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08020204   0x08020204   0x00000002   Code   RO         2101    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08020206   0x08020206   0x00000000   Code   RO         2127    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08020206   0x08020206   0x00000000   Code   RO         2129    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08020206   0x08020206   0x00000000   Code   RO         2131    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08020206   0x08020206   0x00000000   Code   RO         2134    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08020206   0x08020206   0x00000000   Code   RO         2137    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08020206   0x08020206   0x00000000   Code   RO         2139    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08020206   0x08020206   0x00000000   Code   RO         2142    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08020206   0x08020206   0x00000002   Code   RO         2143    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08020208   0x08020208   0x00000000   Code   RO         1979    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08020208   0x08020208   0x00000000   Code   RO         1985    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08020208   0x08020208   0x00000006   Code   RO         1997    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0802020e   0x0802020e   0x00000000   Code   RO         1987    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0802020e   0x0802020e   0x00000004   Code   RO         1988    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08020212   0x08020212   0x00000000   Code   RO         1990    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08020212   0x08020212   0x00000008   Code   RO         1991    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0802021a   0x0802021a   0x00000002   Code   RO         2018    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0802021c   0x0802021c   0x00000000   Code   RO         2074    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0802021c   0x0802021c   0x00000004   Code   RO         2075    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08020220   0x08020220   0x00000006   Code   RO         2076    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08020226   0x08020226   0x00000002   PAD
    0x08020228   0x08020228   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08020268   0x08020268   0x000000ee   Code   RO         1971    .text               c_w.l(lludivv7m.o)
    0x08020356   0x08020356   0x0000004e   Code   RO         1973    .text               c_w.l(rt_memclr_w.o)
    0x080203a4   0x080203a4   0x00000006   Code   RO         1975    .text               c_w.l(heapauxi.o)
    0x080203aa   0x080203aa   0x0000004a   Code   RO         2001    .text               c_w.l(sys_stackheap_outer.o)
    0x080203f4   0x080203f4   0x00000012   Code   RO         2003    .text               c_w.l(exit.o)
    0x08020406   0x08020406   0x00000002   PAD
    0x08020408   0x08020408   0x00000008   Code   RO         2015    .text               c_w.l(libspace.o)
    0x08020410   0x08020410   0x0000000c   Code   RO         2071    .text               c_w.l(sys_exit.o)
    0x0802041c   0x0802041c   0x00000002   Code   RO         2090    .text               c_w.l(use_no_semi.o)
    0x0802041e   0x0802041e   0x00000000   Code   RO         2092    .text               c_w.l(indicate_semi.o)
    0x0802041e   0x0802041e   0x00000002   Code   RO          262    i.BusFault_Handler  stm32f4xx_it.o
    0x08020420   0x08020420   0x00000002   Code   RO          263    i.DebugMon_Handler  stm32f4xx_it.o
    0x08020422   0x08020422   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08020426   0x08020426   0x00000022   Code   RO          365    i.HAL_CRC_Init      stm32f4xx_hal_crc.o
    0x08020448   0x08020448   0x00000028   Code   RO          179    i.HAL_CRC_MspInit   crc.o
    0x08020470   0x08020470   0x00000024   Code   RO         1320    i.HAL_Delay         stm32f4xx_hal.o
    0x08020494   0x08020494   0x000001f0   Code   RO          776    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08020684   0x08020684   0x00000010   Code   RO          779    i.HAL_GPIO_TogglePin  stm32f4xx_hal_gpio.o
    0x08020694   0x08020694   0x0000000a   Code   RO          780    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x0802069e   0x0802069e   0x00000002   PAD
    0x080206a0   0x080206a0   0x0000000c   Code   RO         1326    i.HAL_GetTick       stm32f4xx_hal.o
    0x080206ac   0x080206ac   0x00000010   Code   RO         1332    i.HAL_IncTick       stm32f4xx_hal.o
    0x080206bc   0x080206bc   0x00000034   Code   RO         1333    i.HAL_Init          stm32f4xx_hal.o
    0x080206f0   0x080206f0   0x00000040   Code   RO         1334    i.HAL_InitTick      stm32f4xx_hal.o
    0x08020730   0x08020730   0x00000030   Code   RO          337    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08020760   0x08020760   0x00000040   Code   RO         1174    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080207a0   0x080207a0   0x00000024   Code   RO         1175    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080207c4   0x080207c4   0x00000134   Code   RO          422    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080208f8   0x080208f8   0x00000020   Code   RO          429    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08020918   0x08020918   0x00000020   Code   RO          430    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08020938   0x08020938   0x00000060   Code   RO          431    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08020998   0x08020998   0x0000036c   Code   RO          434    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08020d04   0x08020d04   0x00000028   Code   RO         1179    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08020d2c   0x08020d2c   0x00000064   Code   RO         1601    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08020d90   0x08020d90   0x0000006c   Code   RO          221    i.HAL_UART_MspInit  usart.o
    0x08020dfc   0x08020dfc   0x00000002   Code   RO          264    i.HardFault_Handler  stm32f4xx_it.o
    0x08020dfe   0x08020dfe   0x00000002   PAD
    0x08020e00   0x08020e00   0x00000024   Code   RO          180    i.MX_CRC_Init       crc.o
    0x08020e24   0x08020e24   0x0000006c   Code   RO          154    i.MX_GPIO_Init      gpio.o
    0x08020e90   0x08020e90   0x00000038   Code   RO          222    i.MX_USART1_UART_Init  usart.o
    0x08020ec8   0x08020ec8   0x00000002   Code   RO          265    i.MemManage_Handler  stm32f4xx_it.o
    0x08020eca   0x08020eca   0x00000002   Code   RO          266    i.NMI_Handler       stm32f4xx_it.o
    0x08020ecc   0x08020ecc   0x00000002   Code   RO          267    i.PendSV_Handler    stm32f4xx_it.o
    0x08020ece   0x08020ece   0x00000002   Code   RO          268    i.SVC_Handler       stm32f4xx_it.o
    0x08020ed0   0x08020ed0   0x00000004   Code   RO          269    i.SysTick_Handler   stm32f4xx_it.o
    0x08020ed4   0x08020ed4   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x08020f68   0x08020f68   0x00000010   Code   RO         1935    i.SystemInit        system_stm32f4xx.o
    0x08020f78   0x08020f78   0x0000010c   Code   RO         1627    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08021084   0x08021084   0x00000002   Code   RO          270    i.UsageFault_Handler  stm32f4xx_it.o
    0x08021086   0x08021086   0x00000020   Code   RO         1181    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080210a6   0x080210a6   0x00000002   PAD
    0x080210a8   0x080210a8   0x00000030   Code   RO           15    i.main              main.o
    0x080210d8   0x080210d8   0x0000000a   Code   RO         2086    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080210e2   0x080210e2   0x00000010   Data   RO         1936    .constdata          system_stm32f4xx.o
    0x080210f2   0x080210f2   0x00000008   Data   RO         1937    .constdata          system_stm32f4xx.o
    0x080210fa   0x080210fa   0x00000002   PAD
    0x080210fc   0x080210fc   0x00000020   Data   RO         2144    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0802111c, Size: 0x000012c0, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0802111c   0x00000008   Data   RW          181    .data               crc.o
    0x20000008   0x08021124   0x0000000c   Data   RW         1340    .data               stm32f4xx_hal.o
    0x20000014   0x08021130   0x00000004   Data   RW         1938    .data               system_stm32f4xx.o
    0x20000018        -       0x00000048   Zero   RW          223    .bss                usart.o
    0x20000060        -       0x00000060   Zero   RW         2016    .bss                c_w.l(libspace.o)
    0x200000c0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x200002c0        -       0x00001000   Zero   RW            1    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        76         18          0          8          0       1675   crc.o
       108         10          0          0          0        991   gpio.o
       200         14          0          0          0     661072   main.o
        64         26        392          0       4608        880   startup_stm32f407xx.o
       180         28          0         12          0       9709   stm32f4xx_hal.o
       172         14          0          0          0      33330   stm32f4xx_hal_cortex.o
        34          0          0          0          0       1131   stm32f4xx_hal_crc.o
       522         46          0          0          0       2981   stm32f4xx_hal_gpio.o
        48          6          0          0          0        934   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5476   stm32f4xx_hal_rcc.o
       368         10          0          0          0       2156   stm32f4xx_hal_uart.o
        20          0          0          0          0       4502   stm32f4xx_it.o
        16          4         24          4          0       1235   system_stm32f4xx.o
       164         22          0          0         72       1866   usart.o

    ----------------------------------------------------------------------
      3322        <USER>        <GROUP>         24       4680     727938   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          2          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       608         <USER>          <GROUP>          0         96        880   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       592         16          0          0         96        764   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       608         <USER>          <GROUP>          0         96        880   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3930        286        450         24       4776     725358   Grand Totals
      3930        286        450         24       4776     725358   ELF Image Totals
      3930        286        450         24          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 4380 (   4.28kB)
    Total RW  Size (RW Data + ZI Data)              4800 (   4.69kB)
    Total ROM Size (Code + RO Data + RW Data)       4404 (   4.30kB)

==============================================================================

