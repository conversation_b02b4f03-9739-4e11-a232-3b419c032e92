--cpu=Cortex-M4.fp.sp
"stm32f407vgt6_bootloader\startup_stm32f407xx.o"
"stm32f407vgt6_bootloader\main.o"
"stm32f407vgt6_bootloader\gpio.o"
"stm32f407vgt6_bootloader\crc.o"
"stm32f407vgt6_bootloader\usart.o"
"stm32f407vgt6_bootloader\stm32f4xx_it.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_msp.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_crc.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_rcc.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_rcc_ex.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_flash.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_flash_ex.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_flash_ramfunc.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_gpio.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_dma_ex.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_dma.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_pwr.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_pwr_ex.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_cortex.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_exti.o"
"stm32f407vgt6_bootloader\stm32f4xx_hal_uart.o"
"stm32f407vgt6_bootloader\system_stm32f4xx.o"
--strict --scatter "STM32F407VGT6_Bootloader\STM32F407VGT6_Bootloader.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "STM32F407VGT6_Bootloader.map" -o STM32F407VGT6_Bootloader\STM32F407VGT6_Bootloader.axf